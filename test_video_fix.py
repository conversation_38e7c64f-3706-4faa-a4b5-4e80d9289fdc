#!/usr/bin/env python3
"""
Test script to verify video processing fixes
"""
import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.video.processor import VideoProcessor
from src.utils.config import ensure_directories

async def test_video_processor():
    """Test the video processor with sample data"""
    print("Testing VideoProcessor...")

    # Ensure directories exist
    ensure_directories()

    # Create a test session
    session_id = "test_session_001"
    processor = VideoProcessor(session_id)

    # Start recording
    await processor.start_recording(1280, 720)
    print(f"Started recording for session: {session_id}")

    # Simulate some H.264 video chunks
    # This is a minimal H.264 NAL unit start code
    sample_h264_chunk = b'\x00\x00\x00\x01' + b'\x67' + b'\x00' * 100  # SPS NAL unit

    # Add some video chunks
    for i in range(5):
        await processor.add_video_chunk(sample_h264_chunk + bytes([i] * 50))
        print(f"Added video chunk {i+1}")

    # Stop recording
    await processor.stop_recording()
    print("Stopped recording")

    # Check if files were created
    if processor.original_video_path.exists():
        size = processor.original_video_path.stat().st_size
        print(f"✅ Video file created: {processor.original_video_path} ({size} bytes)")

        # Try to get video info using ffprobe
        try:
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(processor.original_video_path)
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Video file is valid and readable by FFmpeg")
            else:
                print(f"❌ Video file validation failed: {result.stderr}")
        except FileNotFoundError:
            print("⚠️  FFprobe not found, cannot validate video file")
    else:
        print("❌ Video file was not created")

    # Cleanup
    processor.cleanup()
    print("Cleanup completed")

async def test_existing_video_files():
    """Test existing video files to see if they're corrupted"""
    print("\nTesting existing video files...")

    video_dir = Path("recordings/original_video")
    if not video_dir.exists():
        print("No video directory found")
        return

    video_files = list(video_dir.glob("*.mp4"))
    print(f"Found {len(video_files)} video files")

    for video_file in video_files[:3]:  # Test first 3 files
        size = video_file.stat().st_size
        print(f"\nTesting: {video_file.name} ({size} bytes)")

        if size < 1000:  # Very small files are likely corrupted
            print("❌ File is too small, likely corrupted")
            continue

        try:
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', str(video_file)
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Video file is valid")
            else:
                print(f"❌ Video file is corrupted: {result.stderr}")
        except FileNotFoundError:
            print("⚠️  FFprobe not found, cannot validate video file")

if __name__ == "__main__":
    print("🎥 Video Processing Fix Test")
    print("=" * 40)

    asyncio.run(test_video_processor())
    asyncio.run(test_existing_video_files())

    print("\n" + "=" * 40)
    print("Test completed!")