#!/usr/bin/env python3
"""
Test script to verify video processing fixes
"""
import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.services.video.processor import VideoProcessor
from src.utils.config import ensure_directories

async def test_video_processor():
    """Test the video processor with a real test video"""
    print("Testing VideoProcessor...")

    # Ensure directories exist
    ensure_directories()

    # Create a test session
    session_id = "test_session_001"
    processor = VideoProcessor(session_id)

    print("⚠️  Note: This test creates a synthetic video since we can't simulate real WebM chunks")
    print("   For real testing, use the browser client to record actual video.")

    # Start recording
    await processor.start_recording(1280, 720)
    print(f"Started recording for session: {session_id}")

    # Instead of fake WebM chunks, create a test video using FFmpeg
    # This simulates what would happen with real video data
    try:
        import subprocess
        import tempfile

        # Create a temporary test video (5 seconds of color bars)
        with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
            temp_video_path = temp_file.name

        # Generate test video with FFmpeg
        cmd = [
            'ffmpeg', '-y', '-f', 'lavfi',
            '-i', 'testsrc2=duration=3:size=640x480:rate=30',
            '-c:v', 'libvpx-vp9', '-b:v', '1M',
            temp_video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            # Read the test video and add it as a single chunk
            with open(temp_video_path, 'rb') as f:
                test_video_data = f.read()

            await processor.add_video_chunk(test_video_data)
            print(f"Added test video chunk ({len(test_video_data)} bytes)")

            # Clean up temp file
            import os
            os.unlink(temp_video_path)
        else:
            print("⚠️  Could not create test video, using fallback method")
            # Fallback: just test the file creation without real video data
            await processor.add_video_chunk(b'fake_data')

    except Exception as e:
        print(f"⚠️  Test video creation failed: {e}")
        # Fallback: just test the file creation
        await processor.add_video_chunk(b'fake_data')

    # Stop recording
    await processor.stop_recording()
    print("Stopped recording")

    # Check if files were created
    if processor.original_video_path.exists():
        size = processor.original_video_path.stat().st_size
        print(f"✅ Video file created: {processor.original_video_path} ({size} bytes)")

        # Try to get video info using ffprobe
        try:
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(processor.original_video_path)
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Video file is valid and readable by FFmpeg")

                # Try to extract a frame to verify content
                frame_path = processor.original_video_path.with_suffix('.png')
                frame_cmd = [
                    'ffmpeg', '-y', '-i', str(processor.original_video_path),
                    '-vframes', '1', '-q:v', '2', str(frame_path)
                ]
                frame_result = subprocess.run(frame_cmd, capture_output=True, text=True)

                if frame_result.returncode == 0 and frame_path.exists():
                    print(f"✅ Frame extracted: {frame_path}")
                    # Try to open the frame
                    try:
                        subprocess.run(['open', str(frame_path)], check=False)
                    except:
                        pass
                else:
                    print("⚠️  Could not extract frame from video")
            else:
                print(f"❌ Video file validation failed: {result.stderr}")
        except FileNotFoundError:
            print("⚠️  FFprobe not found, cannot validate video file")
    else:
        print("❌ Video file was not created")

    # Cleanup
    processor.cleanup()
    print("Cleanup completed")

async def test_existing_video_files():
    """Test existing video files to see if they're corrupted"""
    print("\nTesting existing video files...")

    video_dir = Path("recordings/original_video")
    if not video_dir.exists():
        print("No video directory found")
        return

    video_files = list(video_dir.glob("*.mp4"))
    print(f"Found {len(video_files)} video files")

    for video_file in video_files[:3]:  # Test first 3 files
        size = video_file.stat().st_size
        print(f"\nTesting: {video_file.name} ({size} bytes)")

        if size < 1000:  # Very small files are likely corrupted
            print("❌ File is too small, likely corrupted")
            continue

        try:
            import subprocess
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', str(video_file)
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Video file is valid")
            else:
                print(f"❌ Video file is corrupted: {result.stderr}")
        except FileNotFoundError:
            print("⚠️  FFprobe not found, cannot validate video file")

if __name__ == "__main__":
    print("🎥 Video Processing Fix Test")
    print("=" * 40)

    asyncio.run(test_video_processor())
    asyncio.run(test_existing_video_files())

    print("\n" + "=" * 40)
    print("Test completed!")