# 🔐 Authentication & Meeting System Guide

## Overview

Your Omnispeak backend now includes a comprehensive **authentication and meeting management system** with the following features:

### 🎯 **What's New**

1. **🔐 Dual Authentication System**
   - **Host Auth**: Email/password with secure cookie sessions
   - **Guest Auth**: Anonymous authentication with session cookies

2. **👤 Host Features**
   - Personal profile management
   - Voice sample upload & storage
   - Original language configuration
   - Meeting creation & management

3. **🎯 Meeting System**
   - Unique meeting codes for easy joining
   - Multi-language support (max 5 languages per meeting)
   - Cloudflare Stream integration per language
   - Real-time participant management

4. **🌍 Guest Features**
   - Anonymous meeting joining
   - Language selection from available streams
   - No registration required

## 🏗️ Architecture

### New Directory Structure
```
src/
├── auth/                   # 🔐 Authentication System
│   ├── models.py          # User, Session, Auth models
│   ├── service.py         # Authentication logic
│   └── dependencies.py   # FastAPI auth dependencies
├── meetings/              # 🎯 Meeting System
│   └── service.py         # Meeting management logic
├── api/
│   ├── auth_routes.py     # Auth API endpoints
│   ├── meeting_routes.py  # Meeting API endpoints
│   ├── database.py        # Database connection
│   └── models.py          # Meeting & participant models
```

## 🔑 Authentication System

### Host Authentication
```python
# Register new host
POST /auth/register
{
    "email": "<EMAIL>",
    "password": "secure_password",
    "full_name": "John Doe",
    "preferred_language": "en",
    "original_language": "en"
}

# Login host
POST /auth/login
{
    "email": "<EMAIL>",
    "password": "secure_password"
}
```

### Guest Authentication
```python
# Create guest session
POST /auth/guest
{
    "guest_name": "Anonymous User"  # Optional
}
```

### Session Management
- **Secure HTTP-only cookies** for session storage
- **7-day expiration** for hosts, **1-day** for guests
- **Automatic cleanup** of expired sessions

## 🎯 Meeting System

### Meeting Creation (Host Only)
```python
POST /meetings/
{
    "title": "Weekly Team Meeting",
    "description": "Our weekly sync meeting",
    "host_language": "en",
    "is_public": true
}

# Response includes unique meeting code
{
    "id": 1,
    "meeting_code": "ABC123",
    "title": "Weekly Team Meeting",
    "languages": [
        {
            "language_code": "en",
            "language_name": "English",
            "status": "ready"
        }
    ]
}
```

### Adding Languages to Meeting
```python
POST /meetings/{meeting_id}/languages
{
    "language_code": "es",
    "language_name": "Spanish"
}
```

### Joining Meetings
```python
# Anyone can join with meeting code
POST /meetings/join
{
    "meeting_code": "ABC123",
    "display_name": "John Doe",
    "selected_language": "es"  # Optional
}
```

## 🌍 Multi-Language Support

### Language Limits
- **Maximum 5 languages** per meeting
- **Cloudflare Stream integration** for each language
- **Real-time stream management**

### Language Stream Flow
```
1. Host creates meeting with original language
2. Host adds additional languages (up to 5 total)
3. Translation pipeline creates streams for each language
4. Guests select their preferred language when joining
5. Real-time translation streams to selected language
```

## 🔧 API Endpoints

### Authentication Endpoints
```
POST   /auth/register          # Register host
POST   /auth/login             # Login host  
POST   /auth/guest             # Create guest session
POST   /auth/logout            # Logout (clear session)
GET    /auth/me                # Get current user profile
PUT    /auth/profile           # Update host profile
POST   /auth/voice-sample      # Upload voice sample
GET    /auth/status            # Check auth status
```

### Meeting Endpoints
```
POST   /meetings/              # Create meeting (host)
GET    /meetings/my-meetings   # Get host's meetings
GET    /meetings/{id}          # Get meeting details
PUT    /meetings/{id}          # Update meeting
POST   /meetings/{id}/languages # Add language to meeting
GET    /meetings/{id}/languages # Get meeting languages
POST   /meetings/join          # Join meeting (anyone)
GET    /meetings/code/{code}   # Get meeting by code (public)
```

## 🛡️ Security Features

### Password Security
- **bcrypt hashing** with salt
- **Secure password verification**
- **No plaintext storage**

### Session Security
- **HTTP-only cookies** (not accessible via JavaScript)
- **Secure flag** for HTTPS
- **SameSite protection** against CSRF
- **Automatic expiration**

### Authorization
- **Role-based access** (host vs guest)
- **Meeting ownership** verification
- **Private meeting** protection

## 💾 Database Models

### User Model
```python
class User(SQLModel, table=True):
    id: int
    email: str (unique)
    hashed_password: str
    full_name: str
    preferred_language: str
    original_language: str
    voice_sample_path: str
    voice_sample_url: str  # Cloudflare URL
    is_active: bool
    created_at: datetime
    updated_at: datetime
```

### Meeting Model
```python
class Meeting(SQLModel, table=True):
    id: int
    host_id: int
    title: str
    description: str
    host_language: str
    meeting_code: str (unique)
    status: str  # created, live, ended
    is_public: bool
    created_at: datetime
```

### Meeting Language Model
```python
class MeetingLanguage(SQLModel, table=True):
    id: int
    meeting_id: int
    language_code: str
    language_name: str
    stream_url: str  # Cloudflare Stream URL
    cloudflare_video_uid: str
    status: str  # pending, ready, streaming
```

## 🚀 Usage Examples

### Complete Host Workflow
```python
# 1. Register as host
POST /auth/register
{
    "email": "<EMAIL>",
    "password": "secure123",
    "full_name": "Meeting Host",
    "original_language": "en"
}

# 2. Upload voice sample
POST /auth/voice-sample
# Upload audio file

# 3. Create meeting
POST /meetings/
{
    "title": "Product Demo",
    "host_language": "en"
}
# Returns: meeting_code: "XYZ789"

# 4. Add Spanish translation
POST /meetings/1/languages
{
    "language_code": "es",
    "language_name": "Spanish"
}

# 5. Start meeting
PUT /meetings/1
{
    "status": "live"
}
```

### Complete Guest Workflow
```python
# 1. Create guest session
POST /auth/guest
{
    "guest_name": "John Visitor"
}

# 2. Check meeting availability
GET /meetings/code/XYZ789

# 3. Join meeting
POST /meetings/join
{
    "meeting_code": "XYZ789",
    "display_name": "John Visitor",
    "selected_language": "es"
}

# 4. Connect to WebSocket for real-time translation
ws://localhost:8000/ws/es
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_auth_and_meetings.py
```

## 📝 Next Steps

1. **Install new dependencies**:
   ```bash
   uv sync  # Installs bcrypt and email-validator
   ```

2. **Test the system**:
   ```bash
   python test_auth_and_meetings.py
   ```

3. **Start the server**:
   ```bash
   uvicorn main:app --reload
   ```

4. **Try the API**:
   - Register a host at `POST /auth/register`
   - Create a meeting at `POST /meetings/`
   - Join as guest at `POST /auth/guest` then `POST /meetings/join`

## 🎉 Benefits

- **Professional authentication** system
- **Scalable meeting management**
- **Multi-language support** with stream isolation
- **Secure session handling**
- **Easy guest access** without registration
- **Cloudflare integration** for reliable streaming
- **Clean API design** following REST principles

Your Omnispeak backend is now a **complete live translation platform** ready for production use! 🚀
