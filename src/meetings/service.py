"""
Meeting Service

Handles meeting creation, management, and language stream coordination.
"""
import secrets
import string
from datetime import datetime
from typing import List, Optional, Dict
from sqlmodel import Session as DBSession, select
from fastapi import HTTPException, status

from src.api.models import (
    Meeting, MeetingLanguage, MeetingParticipant, TranslationSession,
    MeetingCreate, MeetingUpdate, MeetingLanguageCreate, MeetingJoin,
    MAX_LANGUAGES_PER_MEETING
)
from src.auth.models import User, Session


class MeetingService:
    def delete_meeting(self, meeting_id: int) -> bool:
        """Delete a meeting and all related participants/languages."""
        meeting = self.get_meeting_by_id(meeting_id)
        if not meeting:
            return False
        # Delete participants
        self.db.exec(select(MeetingParticipant).where(MeetingParticipant.meeting_id == meeting_id))
        for participant in self.db.exec(select(MeetingParticipant).where(MeetingParticipant.meeting_id == meeting_id)).all():
            self.db.delete(participant)
        # Delete languages
        for lang in self.db.exec(select(MeetingLanguage).where(MeetingLanguage.meeting_id == meeting_id)).all():
            self.db.delete(lang)
        self.db.delete(meeting)
        self.db.commit()
        return True
    def create_initial_user_if_none(self, username: str, password: str) -> bool:
        """Create initial user if no users exist in DB. Returns True if created."""
        from src.auth.models import User
        user_count = len(self.db.exec(select(User)).all())
        if user_count == 0:
            password_hash = User.hash_password(password)
            user = User(
                email=f"{username}@omnispeak.dev",
                hashed_password=password_hash,
                full_name="Admin User",
                preferred_language="en",
                original_language="ru",
                is_active=True
            )
            self.db.add(user)
            self.db.commit()
            self.db.refresh(user)
            return True
        return False

    def add_user(self, email: str, password: str, creator_user_id: int) -> dict:
        """Add a new user if creator_user_id is valid. Returns user info."""
        from src.auth.models import User
        # Check creator exists
        creator = self.db.exec(select(User).where(User.id == creator_user_id)).first()
        if not creator:
            raise Exception("Creator user not found")
        # Check email not taken
        existing = self.db.exec(select(User).where(User.email == email)).first()
        if existing:
            raise Exception("Email already exists")
        password_hash = User.hash_password(password)
        user = User(
            email=email,
            hashed_password=password_hash,
            full_name="New User",
            preferred_language="en",
            original_language="en",
            is_active=True
        )
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return {"id": user.id, "email": user.email}
    def authenticate_speaker(self, email: str, password: str) -> Optional[User]:
        """Authenticate speaker by email and password (bcrypt)"""
        from src.auth.models import User
        user = self.db.exec(select(User).where(User.email == email)).first()
        if user and user.hashed_password:
            if User.verify_password(user, password):
                return user
        return None

    def get_public_rooms(self) -> list:
        """Return all public rooms"""
        rooms = self.db.exec(select(Meeting).where(Meeting.is_public == True)).all()
        return [{
            "id": r.id,
            "title": r.title,
            "description": r.description,
            "host_language": r.host_language,
            "member_count": len(getattr(r, "participants", [])),
            "created_at": r.created_at
        } for r in rooms]

    def get_private_rooms(self, user_id: int) -> list:
        """Return private rooms belonging to user"""
        if not user_id:
            return []
        rooms = self.db.exec(select(Meeting).where(Meeting.is_public == False, Meeting.host_id == user_id)).all()
        return [{
            "id": r.id,
            "title": r.title,
            "description": r.description,
            "host_language": r.host_language,
            "member_count": len(getattr(r, "participants", [])),
            "created_at": r.created_at
        } for r in rooms]

    def add_viewer_to_room(self, room_id: int, viewer_id: str, viewer_name: str = None):
        """Add viewer to room (for stats), prevent duplicate joins."""
        # Check if viewer already joined
        existing = self.db.exec(select(MeetingParticipant).where(
            MeetingParticipant.meeting_id == room_id,
            MeetingParticipant.viewer_id == viewer_id,
            MeetingParticipant.participant_type == "viewer"
        )).first()
        if existing:
            return existing  # Don't add duplicate, just return
        participant = MeetingParticipant(
            meeting_id=room_id,
            user_id=None,
            session_id=None,
            participant_type="viewer",
            display_name=viewer_name or "Viewer",
            selected_language=None,
            viewer_id=viewer_id
        )
        self.db.add(participant)
        self.db.commit()
        self.db.refresh(participant)
        return participant

    def get_room_viewer_count(self, room_id: int) -> int:
        """Return count of viewers in room"""
        return len(self.db.exec(select(MeetingParticipant).where(MeetingParticipant.meeting_id == room_id, MeetingParticipant.participant_type == "viewer")).all())

    def get_room_speaker_info(self, room_id: int) -> dict:
        """Return basic info about the room's speaker/host"""
        meeting = self.get_meeting_by_id(room_id)
        if not meeting:
            return {}
        host = self.db.exec(select(User).where(User.id == meeting.host_id)).first()
        return {
            "host_id": meeting.host_id,
            "host_name": getattr(host, "full_name", "Unknown"),
            "host_language": meeting.host_language
        }

    def remove_viewer_from_room(self, room_id: int, viewer_id: str):
        """Remove viewer from room (decrement count), prevent deleting already deleted viewers."""
        participant = self.db.exec(select(MeetingParticipant).where(
            MeetingParticipant.meeting_id == room_id,
            MeetingParticipant.viewer_id == viewer_id,
            MeetingParticipant.participant_type == "viewer"
        )).first()
        if not participant:
            return False  # Already deleted
        self.db.delete(participant)
        self.db.commit()
        return True

    def get_room_viewer_ids(self, room_id: int) -> list:
        """Return all viewer IDs for a room."""
        viewers = self.db.exec(select(MeetingParticipant).where(
            MeetingParticipant.meeting_id == room_id,
            MeetingParticipant.participant_type == "viewer"
        )).all()
        return [v.viewer_id for v in viewers if v.viewer_id]

    def set_room_lang_limit(self, room_id: int, lang_limit: int):
        """Set the language limit for a room"""
        meeting = self.get_meeting_by_id(room_id)
        if not meeting:
            raise HTTPException(status_code=404, detail="Room not found")
        meeting.lang_limit = lang_limit
        self.db.commit()
        self.db.refresh(meeting)

    def get_room_chat(self, room_id: int, user_id: int = None, viewer_id: str = None) -> list:
        """Get chat messages for a room"""
        # Assuming a ChatMessage model with room_id, user_id, viewer_id
        from src.api.models import ChatMessage
        query = select(ChatMessage).where(ChatMessage.room_id == room_id)
        if user_id:
            # Return all messages for room
            pass
        elif viewer_id:
            # Return only messages sent by this viewer
            query = query.where(ChatMessage.viewer_id == viewer_id)
        messages = self.db.exec(query).all()
        return [{
            "id": m.id,
            "text": m.text,
            "sender_id": m.user_id or m.viewer_id,
            "sender_name": m.sender_name,
            "timestamp": m.timestamp
        } for m in messages]

    def add_room_chat_message(self, room_id: int, user_id: int, viewer_id: str, viewer_name: str, text: str):
        """Add a chat message to room's database"""
        from src.api.models import ChatMessage
        msg = ChatMessage(
            room_id=room_id,
            user_id=user_id,
            viewer_id=viewer_id,
            sender_name=viewer_name,
            text=text,
            timestamp=datetime.utcnow()
        )
        self.db.add(msg)
        self.db.commit()
        self.db.refresh(msg)
        return msg
    """Service for managing meetings and language streams"""
    
    def __init__(self, db_session: DBSession):
        self.db = db_session
    
    def create_meeting(self, host_id: int, meeting_data: MeetingCreate) -> Meeting:
        """Create a new meeting"""
        # Generate unique meeting code
        meeting_code = self._generate_meeting_code()
        
        # Ensure meeting code is unique
        while self._meeting_code_exists(meeting_code):
            meeting_code = self._generate_meeting_code()
        
        meeting = Meeting(
            host_id=host_id,
            title=meeting_data.title,
            description=meeting_data.description,
            host_language=meeting_data.host_language,
            meeting_code=meeting_code,
            is_public=meeting_data.is_public
        )
        
        self.db.add(meeting)
        self.db.commit()
        self.db.refresh(meeting)
        
        # Add host as participant
        self._add_participant(meeting.id, host_id, None, "host", "Host", meeting_data.host_language)
        
        # Create the host language stream
        self.add_language_to_meeting(
            meeting.id,
            MeetingLanguageCreate(
                language_code=meeting_data.host_language,
                language_name=self._get_language_name(meeting_data.host_language)
            )
        )
        
        return meeting
    
    def get_meeting_by_code(self, meeting_code: str) -> Optional[Meeting]:
        """Get meeting by meeting code"""
        return self.db.exec(
            select(Meeting).where(Meeting.meeting_code == meeting_code)
        ).first()
    
    def get_meeting_by_id(self, meeting_id: int) -> Optional[Meeting]:
        """Get meeting by ID"""
        return self.db.exec(
            select(Meeting).where(Meeting.id == meeting_id)
        ).first()
    
    def get_host_meetings(self, host_id: int) -> List[Meeting]:
        """Get all meetings created by a host"""
        return self.db.exec(
            select(Meeting).where(Meeting.host_id == host_id)
        ).all()
    
    def update_meeting(self, meeting_id: int, host_id: int, update_data: MeetingUpdate) -> Meeting:
        """Update meeting details"""
        meeting = self.get_meeting_by_id(meeting_id)
        
        if not meeting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Meeting not found"
            )
        
        if meeting.host_id != host_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only the host can update the meeting"
            )
        
        # Update fields
        if update_data.title is not None:
            meeting.title = update_data.title
        if update_data.description is not None:
            meeting.description = update_data.description
        if update_data.status is not None:
            meeting.status = update_data.status
            if update_data.status == "live" and not meeting.started_at:
                meeting.started_at = datetime.utcnow()
            elif update_data.status == "ended" and not meeting.ended_at:
                meeting.ended_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(meeting)
        
        return meeting
    
    def add_language_to_meeting(self, meeting_id: int, language_data: MeetingLanguageCreate) -> MeetingLanguage:
        """Add a new language stream to a meeting"""
        meeting = self.get_meeting_by_id(meeting_id)
        
        if not meeting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Meeting not found"
            )
        
        # Check language limit
        current_languages = self.db.exec(
            select(MeetingLanguage).where(MeetingLanguage.meeting_id == meeting_id)
        ).all()
        
        if len(current_languages) >= MAX_LANGUAGES_PER_MEETING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Maximum {MAX_LANGUAGES_PER_MEETING} languages allowed per meeting"
            )
        
        # Check if language already exists
        existing_language = self.db.exec(
            select(MeetingLanguage).where(
                MeetingLanguage.meeting_id == meeting_id,
                MeetingLanguage.language_code == language_data.language_code
            )
        ).first()
        
        if existing_language:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Language already exists in this meeting"
            )
        
        language = MeetingLanguage(
            meeting_id=meeting_id,
            language_code=language_data.language_code,
            language_name=language_data.language_name
        )
        
        self.db.add(language)
        self.db.commit()
        self.db.refresh(language)
        
        return language
    
    def join_meeting_as_guest(self, join_data: MeetingJoin, session_id: int) -> Dict:
        """Join meeting as guest"""
        meeting = self.get_meeting_by_code(join_data.meeting_code)
        
        if not meeting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Meeting not found"
            )
        
        if meeting.status not in ["created", "live"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Meeting is not available for joining"
            )
        
        # Add as participant
        participant = self._add_participant(
            meeting.id, None, session_id, "guest", 
            join_data.display_name, join_data.selected_language
        )
        
        # Get available languages
        languages = self.get_meeting_languages(meeting.id)
        
        return {
            "meeting": meeting,
            "participant": participant,
            "available_languages": languages
        }
    
    def join_meeting_as_host(self, meeting_id: int, host_id: int) -> Dict:
        """Join meeting as host"""
        meeting = self.get_meeting_by_id(meeting_id)
        
        if not meeting:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Meeting not found"
            )
        
        if meeting.host_id != host_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not the host of this meeting"
            )
        
        # Update participant status to active
        participant = self.db.exec(
            select(MeetingParticipant).where(
                MeetingParticipant.meeting_id == meeting_id,
                MeetingParticipant.user_id == host_id,
                MeetingParticipant.participant_type == "host"
            )
        ).first()
        
        if participant:
            participant.is_active = True
            self.db.commit()
        
        # Get available languages
        languages = self.get_meeting_languages(meeting.id)
        
        return {
            "meeting": meeting,
            "participant": participant,
            "available_languages": languages
        }
    
    def get_meeting_languages(self, meeting_id: int) -> List[MeetingLanguage]:
        """Get all languages for a meeting"""
        return self.db.exec(
            select(MeetingLanguage).where(MeetingLanguage.meeting_id == meeting_id)
        ).all()
    
    def update_language_stream(self, language_id: int, stream_url: str, cloudflare_uid: str) -> MeetingLanguage:
        """Update language stream with Cloudflare info"""
        language = self.db.exec(
            select(MeetingLanguage).where(MeetingLanguage.id == language_id)
        ).first()
        
        if not language:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Language not found"
            )
        
        language.stream_url = stream_url
        language.cloudflare_video_uid = cloudflare_uid
        language.status = "ready"
        
        self.db.commit()
        self.db.refresh(language)
        
        return language
    
    def _add_participant(self, meeting_id: int, user_id: Optional[int], session_id: Optional[int], 
                        participant_type: str, display_name: str, selected_language: Optional[str]) -> MeetingParticipant:
        """Add participant to meeting"""
        participant = MeetingParticipant(
            meeting_id=meeting_id,
            user_id=user_id,
            session_id=session_id,
            participant_type=participant_type,
            display_name=display_name,
            selected_language=selected_language
        )
        
        self.db.add(participant)
        self.db.commit()
        self.db.refresh(participant)
        
        return participant
    
    def _generate_meeting_code(self) -> str:
        """Generate a random meeting code"""
        return ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
    
    def _meeting_code_exists(self, code: str) -> bool:
        """Check if meeting code already exists"""
        return bool(self.db.exec(
            select(Meeting).where(Meeting.meeting_code == code)
        ).first())
    
    def _get_language_name(self, language_code: str) -> str:
        """Get human-readable language name from code"""
        language_names = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'uk': 'Ukrainian',
            'pl': 'Polish',
            'nl': 'Dutch',
            'sv': 'Swedish',
            'da': 'Danish',
            'no': 'Norwegian',
            'fi': 'Finnish'
        }
        return language_names.get(language_code, language_code.upper())
