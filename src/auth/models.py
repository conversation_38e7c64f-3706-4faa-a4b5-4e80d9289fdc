"""
Authentication Models

Database models for users, sessions, and authentication.
"""
from typing import Optional
from datetime import datetime
from sqlmodel import Field, SQLModel
from pydantic import BaseModel, EmailStr
import bcrypt


class User(SQLModel, table=True):
    """Base user model for hosts"""
    id: Optional[int] = Field(default=None, primary_key=True)
    email: str = Field(unique=True, index=True)
    hashed_password: str
    full_name: str
    preferred_language: str = "en"
    voice_sample_path: Optional[str] = None
    voice_sample_url: Optional[str] = None  # Cloudflare URL
    original_language: str = "en"
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def verify_password(self, password: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.hashed_password.encode('utf-8'))
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password for storage"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')


class Session(SQLModel, table=True):
    """Session model for both hosts and guests"""
    id: Optional[int] = Field(default=None, primary_key=True)
    session_token: str = Field(unique=True, index=True)
    user_id: Optional[int] = Field(default=None, foreign_key="user.id")  # None for guests
    user_type: str  # "host" or "guest"
    guest_name: Optional[str] = None  # For guest sessions
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True


# Pydantic models for API requests/responses
class UserRegister(BaseModel):
    """User registration request"""
    email: EmailStr
    password: str
    full_name: str
    preferred_language: str = "en"
    original_language: str = "en"


class UserLogin(BaseModel):
    """User login request"""
    email: EmailStr
    password: str


class UserProfile(BaseModel):
    """User profile response"""
    id: int
    email: str
    full_name: str
    preferred_language: str
    original_language: str
    voice_sample_url: Optional[str] = None
    created_at: datetime


class UserProfileUpdate(BaseModel):
    """User profile update request"""
    full_name: Optional[str] = None
    preferred_language: Optional[str] = None
    original_language: Optional[str] = None


class GuestJoin(BaseModel):
    """Guest join request"""
    guest_name: Optional[str] = None


class AuthResponse(BaseModel):
    """Authentication response"""
    success: bool
    message: str
    user: Optional[UserProfile] = None
    session_token: Optional[str] = None
