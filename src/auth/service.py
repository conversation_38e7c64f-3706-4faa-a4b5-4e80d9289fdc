"""
Authentication Service

Handles user authentication, session management, and security.
"""
import secrets
from datetime import datetime, timedelta
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from sqlmodel import Session as DBSession, select
from fastapi import HTTPException, status

from .models import User, Session, UserRegister, <PERSON>r<PERSON><PERSON><PERSON>, Guest<PERSON>oin
from src.utils.config import JWT_SECRET


class AuthService:
    """Authentication service for hosts and guests"""
    
    def __init__(self, db_session: DBSession):
        self.db = db_session
        self.session_duration_hours = 24 * 7  # 7 days
    
    def register_host(self, user_data: UserRegister) -> Tuple[User, str]:
        """Register a new host user"""
        # Check if user already exists
        existing_user = self.db.exec(
            select(User).where(User.email == user_data.email)
        ).first()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        hashed_password = User.hash_password(user_data.password)
        user = User(
            email=user_data.email,
            hashed_password=hashed_password,
            full_name=user_data.full_name,
            preferred_language=user_data.preferred_language,
            original_language=user_data.original_language
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        # Create session
        session_token = self._create_session(user.id, "host")
        
        return user, session_token
    
    def login_host(self, login_data: UserLogin) -> Tuple[User, str]:
        """Login host user"""
        user = self.db.exec(
            select(User).where(User.email == login_data.email)
        ).first()
        
        if not user or not user.verify_password(login_data.password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is deactivated"
            )
        
        # Create new session
        session_token = self._create_session(user.id, "host")
        
        return user, session_token
    
    def create_guest_session(self, guest_data: GuestJoin) -> str:
        """Create anonymous guest session"""
        session_token = self._create_session(None, "guest", guest_data.guest_name)
        return session_token
    
    def get_current_user(self, session_token: str) -> Optional[Tuple[Optional[User], Session]]:
        """Get current user from session token"""
        session = self.db.exec(
            select(Session).where(
                Session.session_token == session_token,
                Session.is_active == True,
                Session.expires_at > datetime.utcnow()
            )
        ).first()
        
        if not session:
            return None
        
        user = None
        if session.user_id:
            user = self.db.exec(
                select(User).where(User.id == session.user_id)
            ).first()
        
        return user, session
    
    def logout(self, session_token: str) -> bool:
        """Logout user by deactivating session"""
        session = self.db.exec(
            select(Session).where(Session.session_token == session_token)
        ).first()
        
        if session:
            session.is_active = False
            self.db.commit()
            return True
        
        return False
    
    def update_user_profile(self, user_id: int, update_data: dict) -> User:
        """Update user profile"""
        user = self.db.exec(
            select(User).where(User.id == user_id)
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update fields
        for field, value in update_data.items():
            if hasattr(user, field) and value is not None:
                setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def update_voice_sample(self, user_id: int, file_path: str, cloudflare_url: str) -> User:
        """Update user's voice sample"""
        user = self.db.exec(
            select(User).where(User.id == user_id)
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user.voice_sample_path = file_path
        user.voice_sample_url = cloudflare_url
        user.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def _create_session(self, user_id: Optional[int], user_type: str, guest_name: Optional[str] = None) -> str:
        """Create a new session"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(hours=self.session_duration_hours)
        
        session = Session(
            session_token=session_token,
            user_id=user_id,
            user_type=user_type,
            guest_name=guest_name,
            expires_at=expires_at
        )
        
        self.db.add(session)
        self.db.commit()
        
        return session_token
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        expired_sessions = self.db.exec(
            select(Session).where(Session.expires_at < datetime.utcnow())
        ).all()
        
        for session in expired_sessions:
            session.is_active = False
        
        self.db.commit()
        return len(expired_sessions)
