"""
Module for video processing and audio synchronization
"""
import asyncio
import cv2
import ffmpeg
import numpy as np
import logging
import time
from pathlib import Path
from typing import Optional, Tuple, List
from src.utils import config

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Class for processing video streams"""

    def __init__(self, session_id: str):
        self.session_id = session_id
        self.video_chunks = []
        self.video_timestamps = []
        self.is_recording = False
        self.start_time = None

        # File paths
        self.original_video_path = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_original.mp4"
        self.temp_video_path = Path(config.TEMP_DIR) / f"{session_id}_temp.mp4"
        self.final_video_path = Path(config.FINAL_VIDEO_DIR) / f"{session_id}_final.mp4"
        self.fragments_path = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_fragments.mp4"

        # Video settings
        self.fps = config.VIDEO_FPS
        self.fragments_file = None
        
    async def start_recording(self, width: int = 1280, height: int = 720):
        """
        Starts video recording

        Args:
            width: Video width
            height: Video height
        """
        try:
            # Create raw video file for H.264 chunks
            self.raw_video_path.parent.mkdir(parents=True, exist_ok=True)
            logger.info(f"Creating raw video file: {self.raw_video_path}")
            self.raw_video_file = open(self.raw_video_path, 'wb')

            self.is_recording = True
            self.start_time = time.time()
            logger.info(f"Started video recording: {self.raw_video_path}")

        except Exception as e:
            logger.error(f"Error starting video recording: {e}")
            raise
    
    async def add_video_chunk(self, chunk_data: bytes, timestamp: float = None):
        """
        Adds video chunk to buffer and file

        Args:
            chunk_data: Video chunk data in bytes (H.264 encoded)
            timestamp: Chunk timestamp
        """
        if not self.is_recording:
            return

        try:
            # Add to buffer
            current_time = timestamp or (time.time() - self.start_time)
            self.video_chunks.append(chunk_data)
            self.video_timestamps.append(current_time)

            # Write raw H.264 data to file
            if self.raw_video_file:
                self.raw_video_file.write(chunk_data)
                self.raw_video_file.flush()

            # Limit buffer size
            max_buffer_chunks = int(self.fps * config.VIDEO_BUFFER_SECONDS)
            if len(self.video_chunks) > max_buffer_chunks:
                self.video_chunks.pop(0)
                self.video_timestamps.pop(0)

            logger.debug(f"Added video chunk of {len(chunk_data)} bytes")

        except Exception as e:
            logger.error(f"Error adding video chunk: {e}")

    # Keep the old method for backward compatibility
    async def add_video_frame(self, frame_data: bytes, timestamp: float = None):
        """Legacy method - redirects to add_video_chunk"""
        await self.add_video_chunk(frame_data, timestamp)
    
    async def stop_recording(self):
        """Stops video recording and converts raw H.264 to MP4"""
        try:
            self.is_recording = False

            if self.raw_video_file:
                self.raw_video_file.close()
                self.raw_video_file = None

            # Convert raw H.264 to MP4 using FFmpeg
            if self.raw_video_path.exists() and self.raw_video_path.stat().st_size > 0:
                await self._convert_raw_to_mp4()
                logger.info(f"Stopped video recording and converted to: {self.original_video_path}")
            else:
                logger.warning("No video data recorded or raw file is empty")

        except Exception as e:
            logger.error(f"Error stopping video recording: {e}")

    async def _convert_raw_to_mp4(self):
        """Convert raw H.264 file to MP4 container"""
        try:
            # Ensure output directory exists
            self.original_video_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"Converting {self.raw_video_path} to {self.original_video_path}")
            logger.info(f"Raw file size: {self.raw_video_path.stat().st_size} bytes")

            # Use FFmpeg to convert raw H.264 to MP4
            # For raw H.264, we need to specify the format explicitly
            input_stream = ffmpeg.input(str(self.raw_video_path), f='h264')
            output = ffmpeg.output(
                input_stream,
                str(self.original_video_path),
                vcodec='copy',  # Copy video stream without re-encoding
                f='mp4',
                r=self.fps  # Set frame rate
            )

            # Run with error capture for debugging
            try:
                await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=False)
                logger.info(f"Successfully converted raw video to MP4: {self.original_video_path}")
            except ffmpeg.Error as e:
                logger.error(f"FFmpeg error: {e.stderr.decode() if e.stderr else 'Unknown error'}")
                # Try alternative approach - create a simple MP4 with minimal headers
                await self._create_simple_mp4()

        except Exception as e:
            logger.error(f"Error converting raw video to MP4: {e}")
            # Don't raise - try to create at least an empty MP4 file
            await self._create_simple_mp4()

    async def _create_simple_mp4(self):
        """Create a simple MP4 file as fallback"""
        try:
            # Create a minimal MP4 file that can be played
            # This is a workaround for when raw H.264 conversion fails
            logger.info("Creating simple MP4 file as fallback")

            # Use FFmpeg to create a black video as placeholder
            output = ffmpeg.output(
                ffmpeg.input('color=black:size=1280x720:duration=1', f='lavfi'),
                str(self.original_video_path),
                vcodec='libx264',
                pix_fmt='yuv420p',
                r=self.fps
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Created placeholder MP4 file: {self.original_video_path}")

        except Exception as e:
            logger.error(f"Error creating simple MP4: {e}")
            # As last resort, create an empty file
            self.original_video_path.touch()

    async def merge_with_audio(self, audio_path: Path, output_path: Path = None) -> Path:
        """
        Merges video with new audio track

        Args:
            audio_path: Path to audio file
            output_path: Path for output file (optional)

        Returns:
            Path to merged video file
        """
        if output_path is None:
            output_path = self.final_video_path
        
        try:
            if not self.original_video_path.exists():
                raise FileNotFoundError(f"Original video not found: {self.original_video_path}")

            if not audio_path.exists():
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            # Use ffmpeg for merging
            video_input = ffmpeg.input(str(self.original_video_path))
            audio_input = ffmpeg.input(str(audio_path))

            # Merge settings
            output = ffmpeg.output(
                video_input['v'],  # video stream
                audio_input['a'],  # audio stream
                str(output_path),
                vcodec=config.VIDEO_CODEC,
                acodec=config.AUDIO_CODEC,
                **{
                    'b:v': f'{config.MAX_VIDEO_BITRATE_KBPS}k',
                    'b:a': f'{config.MAX_AUDIO_BITRATE_KBPS}k',
                    'shortest': None  # trim to shortest stream
                }
            )

            # Execute merge
            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)

            logger.info(f"Video merged with audio: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error merging video with audio: {e}")
            raise
    
    async def get_video_duration(self, video_path: Path) -> float:
        """
        Gets video duration in seconds

        Args:
            video_path: Path to video file

        Returns:
            Duration in seconds
        """
        try:
            probe = await asyncio.to_thread(
                ffmpeg.probe, str(video_path)
            )
            duration = float(probe['streams'][0]['duration'])
            return duration
        except Exception as e:
            logger.error(f"Error getting video duration: {e}")
            return 0.0

    async def extract_audio_from_video(self, video_path: Path, audio_path: Path):
        """
        Extracts audio from video file

        Args:
            video_path: Path to video file
            audio_path: Path to save audio
        """
        try:
            input_video = ffmpeg.input(str(video_path))
            audio = input_video.audio

            output = ffmpeg.output(
                audio,
                str(audio_path),
                acodec='pcm_s16le',  # PCM 16-bit for Gemini compatibility
                ar=config.INPUT_SAMPLE_RATE,
                ac=1  # mono
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Audio extracted from video: {audio_path}")

        except Exception as e:
            logger.error(f"Error extracting audio from video: {e}")
            raise

    def cleanup(self):
        """Cleans up temporary files"""
        try:
            if self.temp_video_path.exists():
                self.temp_video_path.unlink()

            if self.raw_video_path.exists():
                self.raw_video_path.unlink()

            # Clear buffer
            self.video_chunks.clear()
            self.video_timestamps.clear()

        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {e}")
