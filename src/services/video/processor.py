"""
Module for video processing and audio synchronization
"""
import asyncio
import cv2
import ffmpeg
import numpy as np
import logging
import time
from pathlib import Path
from typing import Optional, Tuple, List
from src.utils import config

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Class for processing video streams"""

    def __init__(self, session_id: str):
        self.session_id = session_id
        self.video_chunks = []
        self.video_timestamps = []
        self.is_recording = False
        self.start_time = None

        # File paths
        self.original_video_path = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_original.mp4"
        self.temp_video_path = Path(config.TEMP_DIR) / f"{session_id}_temp.mp4"
        self.final_video_path = Path(config.FINAL_VIDEO_DIR) / f"{session_id}_final.mp4"
        self.webm_chunks_path = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_chunks.webm"
        self.chunks_dir = Path(config.ORIGINAL_VIDEO_DIR) / f"{session_id}_chunks"

        # Video settings
        self.fps = config.VIDEO_FPS
        self.webm_file = None
        self.chunk_count = 0
        self.chunk_files = []
        
    async def start_recording(self, width: int = 1280, height: int = 720):
        """
        Starts video recording

        Args:
            width: Video width
            height: Video height
        """
        try:
            # Create directories for chunks
            self.webm_chunks_path.parent.mkdir(parents=True, exist_ok=True)
            self.chunks_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"Creating WebM chunks file: {self.webm_chunks_path}")
            self.webm_file = open(self.webm_chunks_path, 'wb')

            self.is_recording = True
            self.start_time = time.time()
            self.chunk_count = 0
            self.chunk_files = []
            logger.info(f"Started video recording: {self.webm_chunks_path}")

        except Exception as e:
            logger.error(f"Error starting video recording: {e}")
            raise
    
    async def add_video_chunk(self, chunk_data: bytes, timestamp: float = None):
        """
        Adds video chunk to buffer and file

        Args:
            chunk_data: Video chunk data in bytes (WebM container with H.264)
            timestamp: Chunk timestamp
        """
        if not self.is_recording:
            return

        try:
            # Add to buffer
            current_time = timestamp or (time.time() - self.start_time)
            self.video_chunks.append(chunk_data)
            self.video_timestamps.append(current_time)

            # Write WebM chunk data to main file
            if self.webm_file:
                self.webm_file.write(chunk_data)
                self.webm_file.flush()

            # Also save individual chunk for better processing
            chunk_filename = f"chunk_{self.chunk_count:06d}_{int(current_time * 1000)}.webm"
            chunk_path = self.chunks_dir / chunk_filename

            try:
                with open(chunk_path, 'wb') as chunk_file:
                    chunk_file.write(chunk_data)
                self.chunk_files.append(chunk_path)
                logger.debug(f"Saved individual chunk: {chunk_filename}")
            except Exception as e:
                logger.warning(f"Failed to save individual chunk: {e}")

            self.chunk_count += 1

            # Limit buffer size
            max_buffer_chunks = int(self.fps * config.VIDEO_BUFFER_SECONDS)
            if len(self.video_chunks) > max_buffer_chunks:
                self.video_chunks.pop(0)
                self.video_timestamps.pop(0)

            logger.debug(f"Added WebM video chunk {self.chunk_count} of {len(chunk_data)} bytes")

        except Exception as e:
            logger.error(f"Error adding video chunk: {e}")

    # Keep the old method for backward compatibility
    async def add_video_frame(self, frame_data: bytes, timestamp: float = None):
        """Legacy method - redirects to add_video_chunk"""
        await self.add_video_chunk(frame_data, timestamp)
    
    async def stop_recording(self):
        """Stops video recording and converts WebM to MP4"""
        try:
            self.is_recording = False

            if self.webm_file:
                self.webm_file.close()
                self.webm_file = None

            # Convert WebM to MP4 using FFmpeg
            if self.webm_chunks_path.exists() and self.webm_chunks_path.stat().st_size > 0:
                # Try individual chunks first, then fallback to concatenated file
                if len(self.chunk_files) > 0:
                    await self._convert_individual_chunks_to_mp4()
                else:
                    await self._convert_webm_to_mp4()
                logger.info(f"Stopped video recording and converted to: {self.original_video_path}")
            else:
                logger.warning("No video data recorded or WebM file is empty")

        except Exception as e:
            logger.error(f"Error stopping video recording: {e}")

    async def _convert_individual_chunks_to_mp4(self):
        """Convert individual WebM chunks to MP4 using FFmpeg concat"""
        try:
            logger.info(f"Converting {len(self.chunk_files)} individual chunks to MP4")

            # Create concat file list
            concat_file = self.chunks_dir / "concat_list.txt"

            with open(concat_file, 'w') as f:
                for chunk_file in self.chunk_files:
                    if chunk_file.exists() and chunk_file.stat().st_size > 0:
                        f.write(f"file '{chunk_file.absolute()}'\n")

            # Use FFmpeg concat demuxer to properly join the chunks
            input_stream = ffmpeg.input(str(concat_file), f='concat', safe=0)
            output = ffmpeg.output(
                input_stream,
                str(self.original_video_path),
                vcodec='copy',
                acodec='copy',
                f='mp4',
                movflags='faststart'
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=False)
            logger.info(f"Successfully converted individual chunks to MP4: {self.original_video_path}")

            # Extract H.264 stream for Cloudflare
            await self._extract_h264_for_streaming()

        except Exception as e:
            logger.error(f"Error converting individual chunks: {e}")
            # Fallback to concatenated file method
            await self._convert_webm_to_mp4()

    async def _convert_webm_to_mp4(self):
        """Convert WebM chunks to MP4 container and extract H.264 for Cloudflare"""
        try:
            # Ensure output directory exists
            self.original_video_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"Converting {self.webm_chunks_path} to {self.original_video_path}")
            logger.info(f"WebM file size: {self.webm_chunks_path.stat().st_size} bytes")

            # First, try to fix the WebM file by re-muxing it
            # This handles cases where concatenated chunks have conflicting headers
            temp_fixed_webm = self.webm_chunks_path.with_suffix('.fixed.webm')

            try:
                # Re-mux the WebM to fix any header issues
                input_stream = ffmpeg.input(str(self.webm_chunks_path))
                fixed_output = ffmpeg.output(
                    input_stream,
                    str(temp_fixed_webm),
                    vcodec='copy',
                    f='webm',
                    avoid_negative_ts='make_zero'  # Fix timing issues
                )

                await asyncio.to_thread(ffmpeg.run, fixed_output, overwrite_output=True, quiet=True)
                logger.info("Fixed WebM timing and headers")

                # Now convert the fixed WebM to MP4
                input_stream = ffmpeg.input(str(temp_fixed_webm))

            except ffmpeg.Error as e:
                logger.warning(f"WebM fix failed, using original: {e}")
                # Use original file if fixing fails
                input_stream = ffmpeg.input(str(self.webm_chunks_path))

            # Convert to MP4
            output = ffmpeg.output(
                input_stream,
                str(self.original_video_path),
                vcodec='copy',  # Copy video stream without re-encoding
                acodec='copy',  # Copy audio stream if present
                f='mp4',
                movflags='faststart'  # Optimize for streaming
            )

            # Run with error capture for debugging
            try:
                await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=False)
                logger.info(f"Successfully converted WebM to MP4: {self.original_video_path}")

                # Also extract H.264 stream for Cloudflare streaming
                await self._extract_h264_for_streaming()

                # Clean up temp file
                if temp_fixed_webm.exists():
                    temp_fixed_webm.unlink()

            except ffmpeg.Error as e:
                logger.error(f"FFmpeg error: {e.stderr.decode() if e.stderr else 'Unknown error'}")
                # Try alternative approach - create a simple MP4 with minimal headers
                await self._create_simple_mp4()

        except Exception as e:
            logger.error(f"Error converting WebM to MP4: {e}")
            # Don't raise - try to create at least an empty MP4 file
            await self._create_simple_mp4()

    async def _extract_h264_for_streaming(self):
        """Extract H.264 stream from MP4 for Cloudflare streaming"""
        try:
            h264_path = self.original_video_path.with_suffix('.h264')

            input_stream = ffmpeg.input(str(self.original_video_path))
            output = ffmpeg.output(
                input_stream,
                str(h264_path),
                vcodec='copy',
                f='h264'
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Extracted H.264 stream for streaming: {h264_path}")

        except Exception as e:
            logger.error(f"Error extracting H.264 stream: {e}")

    async def _create_simple_mp4(self):
        """Create a simple MP4 file as fallback"""
        try:
            # Create a minimal MP4 file that can be played
            # This is a workaround for when raw H.264 conversion fails
            logger.info("Creating simple MP4 file as fallback")

            # Use FFmpeg to create a black video as placeholder
            output = ffmpeg.output(
                ffmpeg.input('color=black:size=1280x720:duration=1', f='lavfi'),
                str(self.original_video_path),
                vcodec='libx264',
                pix_fmt='yuv420p',
                r=self.fps
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Created placeholder MP4 file: {self.original_video_path}")

        except Exception as e:
            logger.error(f"Error creating simple MP4: {e}")
            # As last resort, create an empty file
            self.original_video_path.touch()

    async def merge_with_audio(self, audio_path: Path, output_path: Path = None) -> Path:
        """
        Merges video with new audio track

        Args:
            audio_path: Path to audio file
            output_path: Path for output file (optional)

        Returns:
            Path to merged video file
        """
        if output_path is None:
            output_path = self.final_video_path
        
        try:
            if not self.original_video_path.exists():
                raise FileNotFoundError(f"Original video not found: {self.original_video_path}")

            if not audio_path.exists():
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            # Use ffmpeg for merging
            video_input = ffmpeg.input(str(self.original_video_path))
            audio_input = ffmpeg.input(str(audio_path))

            # Merge settings
            output = ffmpeg.output(
                video_input['v'],  # video stream
                audio_input['a'],  # audio stream
                str(output_path),
                vcodec=config.VIDEO_CODEC,
                acodec=config.AUDIO_CODEC,
                **{
                    'b:v': f'{config.MAX_VIDEO_BITRATE_KBPS}k',
                    'b:a': f'{config.MAX_AUDIO_BITRATE_KBPS}k',
                    'shortest': None  # trim to shortest stream
                }
            )

            # Execute merge
            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)

            logger.info(f"Video merged with audio: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Error merging video with audio: {e}")
            raise
    
    async def get_video_duration(self, video_path: Path) -> float:
        """
        Gets video duration in seconds

        Args:
            video_path: Path to video file

        Returns:
            Duration in seconds
        """
        try:
            probe = await asyncio.to_thread(
                ffmpeg.probe, str(video_path)
            )
            duration = float(probe['streams'][0]['duration'])
            return duration
        except Exception as e:
            logger.error(f"Error getting video duration: {e}")
            return 0.0

    async def extract_audio_from_video(self, video_path: Path, audio_path: Path):
        """
        Extracts audio from video file

        Args:
            video_path: Path to video file
            audio_path: Path to save audio
        """
        try:
            input_video = ffmpeg.input(str(video_path))
            audio = input_video.audio

            output = ffmpeg.output(
                audio,
                str(audio_path),
                acodec='pcm_s16le',  # PCM 16-bit for Gemini compatibility
                ar=config.INPUT_SAMPLE_RATE,
                ac=1  # mono
            )

            await asyncio.to_thread(ffmpeg.run, output, overwrite_output=True, quiet=True)
            logger.info(f"Audio extracted from video: {audio_path}")

        except Exception as e:
            logger.error(f"Error extracting audio from video: {e}")
            raise

    def cleanup(self):
        """Cleans up temporary files"""
        try:
            if self.temp_video_path.exists():
                self.temp_video_path.unlink()

            if self.webm_chunks_path.exists():
                self.webm_chunks_path.unlink()

            # Clean up individual chunk files
            if self.chunks_dir.exists():
                for chunk_file in self.chunks_dir.glob("*"):
                    try:
                        chunk_file.unlink()
                    except Exception as e:
                        logger.warning(f"Failed to delete chunk file {chunk_file}: {e}")

                # Remove chunks directory if empty
                try:
                    self.chunks_dir.rmdir()
                except Exception:
                    pass  # Directory might not be empty

            # Clear buffer
            self.video_chunks.clear()
            self.video_timestamps.clear()
            self.chunk_files.clear()

        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {e}")
