"""
WebSocket Handlers for Live Translation

Handles WebSocket connections, message processing, and real-time communication
between clients and the translation pipeline.
"""
import asyncio
import json
import logging
import re
import time
import uuid
import wave
from pathlib import Path
from typing import Optional

from fastapi import WebSocket, WebSocketDisconnect

# Import from new structure
from src.core.pipeline import Live<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eline, PipelineConfig, pipeline_manager
from src.services.speech.stt import TranscriptionResult
from src.services.translation.translator import TranslationResult
from src.services.speech.tts import AudioResult
from src.services.video.processor import VideoProcessor
from src.services.cloudflare.stream import CloudflareStreamClient
from src.utils.buffer import buffer_manager
from src.utils.config import (
    INPUT_SAMPLE_RATE, OUTPUT_SAMPLE_RATE, ORIGINAL_AUDIO_DIR,
    TRANSLATED_AUDIO_DIR, CLOUDFLARE_API_TOKEN, CLOUDFLARE_ACCOUNT_ID
)

logger = logging.getLogger(__name__)

# Global connection tracking to prevent rate limit issues
active_connections = set()
MAX_CONCURRENT_CONNECTIONS = 1  # Gladia STT only allows 1 concurrent session


class WebSocketCallbacks:
    """Handles callbacks from the pipeline and sends data back to WebSocket"""
    
    def __init__(self, websocket: WebSocket, session_id: str):
        self.websocket = websocket
        self.session_id = session_id
        self.translated_audio_buffer = bytearray()
        
    async def on_transcription(self, transcription: TranscriptionResult):
        """Handle transcription results"""
        try:
            # Send transcription to client
            await self.websocket.send_json({
                "type": "transcription",
                "text": transcription.text,
                "is_final": transcription.is_final,
                "confidence": transcription.confidence,
                "timestamp": transcription.timestamp
            })
        except Exception as e:
            logger.error(f"Error sending transcription to WebSocket: {e}")
    
    async def on_translation(self, translation: TranslationResult):
        """Handle translation results"""
        try:
            # Send translation to client
            await self.websocket.send_json({
                "type": "translation",
                "original_text": translation.original_text,
                "translated_text": translation.translated_text,
                "target_language": translation.target_language,
                "confidence": translation.confidence,
                "timestamp": translation.timestamp
            })
        except Exception as e:
            logger.error(f"Error sending translation to WebSocket: {e}")
    
    async def on_audio_output(self, audio: AudioResult):
        """Handle TTS audio output"""
        try:
            # Buffer the audio data
            self.translated_audio_buffer.extend(audio.audio_data)
            
            # Send audio data to client as binary
            await self.websocket.send_bytes(audio.audio_data)
            
            logger.info(f"Sent {len(audio.audio_data)} bytes of translated audio to client")
            
            # Save individual translated audio chunks for debugging
            if hasattr(self, 'session_id'):
                await self._save_translated_audio_chunk(audio)
                
        except Exception as e:
            logger.error(f"Error sending audio to WebSocket: {e}")
            
    async def _save_translated_audio_chunk(self, audio: AudioResult):
        """Save individual translated audio chunk"""
        try:
            timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
            safe_session_id = re.sub(r'[^\w\.\-]', '_', self.session_id)
            chunk_path = Path("recordings/translated_audio") / f"chunk_{safe_session_id}_{timestamp}.wav"
            
            # Ensure directory exists
            chunk_path.parent.mkdir(parents=True, exist_ok=True)
            
            with wave.open(str(chunk_path), "wb") as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(OUTPUT_SAMPLE_RATE)
                wf.writeframes(audio.audio_data)
                
            logger.info(f"Saved translated audio chunk: {chunk_path} ({len(audio.audio_data)} bytes)")
        except Exception as e:
            logger.error(f"Error saving translated audio chunk: {e}")


async def handle_websocket_messages(websocket, session_id, pipeline, video_processor, buffer, original_wf):
    """
    Handles messages from WebSocket client using the new pipeline architecture.
    """
    logger.info("Started processing WebSocket messages with new pipeline...")

    room_id = None
    try:
        while True:
            try:
                msg = await websocket.receive()
            except WebSocketDisconnect:
                logger.info("WebSocket disconnected by client.")
                break
            except Exception as e:
                logger.error(f"WebSocket receive error: {e}")
                break

            if msg["type"] == "websocket.disconnect":
                logger.info("WebSocket disconnect event received.")
                break
            elif msg["type"] == "websocket.receive":
                data = msg.get("text")
                if data is not None:
                    # JSON/text message
                    try:
                        payload = json.loads(data)
                        logger.info(f"Received JSON message type={payload.get('type')}")
                        if payload.get('type') == 'join-room':
                            room_id = payload.get('roomId')
                            # Attach buffer/stream to room
                            if hasattr(buffer, 'room_id'):
                                buffer.room_id = room_id
                            if hasattr(buffer, 'stream_manager'):
                                buffer.stream_manager.room_id = room_id
                        await handle_json_message(payload, video_processor, pipeline)
                    except Exception as e:
                        logger.error(f"Error processing JSON message: {e}")
                else:
                    # Binary message (audio/video data)
                    bin_data = msg.get("bytes")
                    if bin_data is not None:
                        logger.info(f"[TRACE] Received binary audio message of size {len(bin_data)} bytes")
                        await handle_binary_message(bin_data, pipeline, video_processor, buffer, original_wf)
                    else:
                        logger.warning(f"[TRACE] Received message with no text or bytes: {msg}")
            else:
                logger.warning(f"Unknown WebSocket message type: {msg['type']}")

    except Exception as e:
        logger.error(f"Error in handle_websocket_messages: {e}")
    finally:
        logger.info("Finished processing WebSocket messages.")


async def handle_binary_message(data: bytes, pipeline: LiveTranslationPipeline, video_processor, buffer, original_wf):
    """Handle binary audio/video data"""
    if len(data) < 10000:
        # Audio data
        logger.info(f"[TRACE] Processing audio chunk of size {len(data)} bytes")
        
        # Add to pipeline for STT processing
        await pipeline.add_audio_chunk(data)
        
        # Still save original audio
        try:
            original_wf.writeframes(data)
        except Exception as e:
            logger.error(f"Failed to write to original audio WAV: {e}")
    else:
        # Video data
        logger.info(f"[TRACE] Received large binary chunk (likely video); size={len(data)} bytes")
        await buffer.add_video_chunk(data)
        await video_processor.add_video_frame(data)
        # Save raw video chunk to file for debugging
        try:
            import os
            from datetime import datetime
            video_dir = Path("recordings/original_video")
            video_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"video_chunk_{timestamp}.mp4"
            chunk_path = video_dir / filename
            with open(chunk_path, "wb") as f:
                f.write(data)
            logger.info(f"Saved video chunk to {chunk_path} ({len(data)} bytes)")
        except Exception as e:
            logger.error(f"Error saving video chunk: {e}")
        logger.info(f"Processed video frame of size {len(data)} bytes")


async def handle_json_message(data: dict, video_processor, pipeline: LiveTranslationPipeline):
    """Handle JSON control messages"""
    try:
        if data.get('type') == 'start_recording':
            width = data.get('width', 1280)
            height = data.get('height', 720)
            await video_processor.start_recording(width, height)
            logger.info(f"Started video recording {width}x{height}")
            
        elif data.get('type') == 'stop_recording':
            await video_processor.stop_recording()
            logger.info("Stopped video recording")
            
        elif data.get('type') == 'join-room':
            room_id = data.get('roomId')
            role = data.get('role', 'guest')
            audio_config = data.get('audioConfig', {})
            
            logger.info(f"Client joined room: {room_id}, role: {role}")
            
            if audio_config:
                client_sample_rate = audio_config.get('sampleRate')
                channel_count = audio_config.get('channelCount', 1)
                encoding = audio_config.get('encoding', 'pcm16')
                
                logger.info(f"Client audio config: sample_rate={client_sample_rate}Hz, channels={channel_count}, encoding={encoding}")
                
                # Log if there's a sample rate mismatch
                if client_sample_rate and client_sample_rate != INPUT_SAMPLE_RATE:
                    logger.warning(f"⚠️ Sample rate mismatch: client={client_sample_rate}Hz, server expects={INPUT_SAMPLE_RATE}Hz")
                    logger.warning("This may cause transcription issues. Consider updating client or server configuration.")
            
        elif data.get('type') == 'end_of_turn':
            logger.info("Received end_of_turn control message; flushing pipeline...")
            # Force flush any pending sentences in the pipeline
            await pipeline.force_flush()
            
        elif data.get('type') == 'chat_message':
            # Handle chat messages
            from src.utils.chat import ChatMessage, ChatDatabase
            from datetime import datetime
            
            message_text = data.get('text')
            room_id = data.get('room_id')
            sender_id = data.get('sender_id')
            
            if message_text and room_id and sender_id:
                chat_db = ChatDatabase()
                chat_message = ChatMessage(
                    text=message_text,
                    sender_id=sender_id,
                    timestamp=datetime.now(),
                    room_id=room_id
                )
                chat_db.save_message(chat_message)
                logger.info(f"Chat message saved to DB: {message_text}")
            else:
                logger.warning("Invalid chat message payload received.")

    except Exception as e:
        logger.error(f"Error processing JSON message: {e}")
