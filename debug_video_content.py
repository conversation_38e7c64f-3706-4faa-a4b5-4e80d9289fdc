#!/usr/bin/env python3
"""
Debug script to analyze video content and help identify issues
"""
import subprocess
import sys
from pathlib import Path

def analyze_video(video_path):
    """Analyze video file and extract diagnostic information"""
    print(f"\n🔍 Analyzing: {video_path}")
    print("=" * 60)
    
    if not video_path.exists():
        print("❌ File does not exist")
        return
    
    size = video_path.stat().st_size
    print(f"📁 File size: {size:,} bytes ({size/1024/1024:.2f} MB)")
    
    if size < 1000:
        print("❌ File too small, likely corrupted")
        return
    
    # Get basic video info
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(video_path)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ FFprobe failed: {result.stderr}")
            return
        
        import json
        info = json.loads(result.stdout)
        
        if 'streams' in info and len(info['streams']) > 0:
            stream = info['streams'][0]
            print(f"✅ Codec: {stream.get('codec_name', 'unknown')}")
            print(f"✅ Resolution: {stream.get('width', '?')}x{stream.get('height', '?')}")
            print(f"✅ Duration: {info.get('format', {}).get('duration', 'unknown')} seconds")
            print(f"✅ Bitrate: {info.get('format', {}).get('bit_rate', 'unknown')} bps")
            
            if 'nb_frames' in stream:
                print(f"✅ Frames: {stream['nb_frames']}")
        
    except Exception as e:
        print(f"❌ Error analyzing video: {e}")
        return
    
    # Extract multiple frames to check content
    print(f"\n🖼️  Extracting sample frames...")
    
    try:
        # Extract frames at different timestamps
        timestamps = ['0.1', '0.5', '1.0', '1.5']
        frame_paths = []
        
        for i, ts in enumerate(timestamps):
            frame_path = video_path.parent / f"debug_frame_{i}_{ts}s.png"
            cmd = [
                'ffmpeg', '-y', '-i', str(video_path),
                '-ss', ts, '-vframes', '1', '-q:v', '2',
                str(frame_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0 and frame_path.exists():
                frame_paths.append(frame_path)
                print(f"✅ Frame at {ts}s: {frame_path.name}")
            else:
                print(f"❌ Failed to extract frame at {ts}s")
        
        if frame_paths:
            print(f"\n📂 Extracted {len(frame_paths)} frames. Open them to check content:")
            for frame_path in frame_paths:
                print(f"   {frame_path}")
                
            # Try to open the first frame
            try:
                subprocess.run(['open', str(frame_paths[0])], check=False)
                print(f"\n👁️  Opened first frame: {frame_paths[0].name}")
            except:
                pass
        
    except Exception as e:
        print(f"❌ Error extracting frames: {e}")
    
    # Check for black/static content
    print(f"\n🎯 Checking for black/static content...")
    try:
        # Use ffmpeg to detect black frames
        cmd = [
            'ffmpeg', '-i', str(video_path), '-vf', 
            'blackdetect=d=0.1:pix_th=0.1', '-f', 'null', '-'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if 'black_start' in result.stderr:
            print("⚠️  Black frames detected in video")
            # Count black frame lines
            black_lines = [line for line in result.stderr.split('\n') if 'black_start' in line]
            print(f"   Found {len(black_lines)} black frame segments")
        else:
            print("✅ No significant black frames detected")
            
    except Exception as e:
        print(f"❌ Error checking black frames: {e}")

def main():
    print("🎥 Video Content Debugger")
    print("=" * 40)
    
    # Find recent video files
    video_dir = Path("recordings/original_video")
    if not video_dir.exists():
        print("❌ No video directory found")
        return
    
    # Get MP4 files sorted by modification time (newest first)
    mp4_files = sorted(
        [f for f in video_dir.glob("*.mp4") if f.stat().st_size > 1000],
        key=lambda x: x.stat().st_mtime,
        reverse=True
    )
    
    if not mp4_files:
        print("❌ No valid MP4 files found")
        return
    
    print(f"Found {len(mp4_files)} video files")
    
    # Analyze the most recent file
    print(f"\n🎯 Analyzing most recent video file:")
    analyze_video(mp4_files[0])
    
    # If there are multiple files, offer to analyze others
    if len(mp4_files) > 1:
        print(f"\n📋 Other recent files:")
        for i, video_file in enumerate(mp4_files[1:4], 1):  # Show up to 3 more
            size = video_file.stat().st_size
            print(f"   {i}. {video_file.name} ({size:,} bytes)")
        
        print(f"\nTo analyze other files, run:")
        print(f"python debug_video_content.py [filename]")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Analyze specific file
        video_path = Path("recordings/original_video") / sys.argv[1]
        analyze_video(video_path)
    else:
        main()
