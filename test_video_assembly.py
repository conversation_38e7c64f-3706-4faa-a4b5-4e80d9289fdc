#!/usr/bin/env python3
"""
Test script to assemble H.264 video chunks into a playable MP4 file
"""
import asyncio
import sys
import subprocess
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_video_assembly():
    """Test assembling H.264 chunks into MP4"""
    print("🎥 Testing Video Assembly from H.264 Chunks")
    print("=" * 50)
    
    video_dir = Path("recordings/original_video")
    h264_files = sorted(list(video_dir.glob("video_chunk_*.h264")))
    
    if not h264_files:
        print("❌ No H.264 chunk files found")
        return
    
    print(f"Found {len(h264_files)} H.264 chunk files:")
    total_size = 0
    for h264_file in h264_files:
        size = h264_file.stat().st_size
        total_size += size
        print(f"  - {h264_file.name}: {size:,} bytes")
    
    print(f"Total size: {total_size:,} bytes")
    
    # Method 1: Concatenate all chunks into one H.264 file
    print("\n📁 Method 1: Concatenating H.264 chunks...")
    combined_h264 = video_dir / "combined_chunks.h264"
    
    try:
        with open(combined_h264, 'wb') as outfile:
            for h264_file in h264_files:
                with open(h264_file, 'rb') as infile:
                    outfile.write(infile.read())
        
        print(f"✅ Combined H.264 file created: {combined_h264} ({combined_h264.stat().st_size:,} bytes)")
        
        # Convert to MP4
        output_mp4 = video_dir / "assembled_video.mp4"
        print(f"🔄 Converting to MP4: {output_mp4}")
        
        cmd = [
            'ffmpeg', '-y',  # Overwrite output
            '-f', 'h264',    # Input format
            '-i', str(combined_h264),
            '-c:v', 'copy',  # Copy video stream
            '-f', 'mp4',     # Output format
            str(output_mp4)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            size = output_mp4.stat().st_size
            print(f"✅ MP4 created successfully: {output_mp4} ({size:,} bytes)")
            
            # Test if the video is playable
            probe_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(output_mp4)
            ]
            
            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True)
            if probe_result.returncode == 0:
                print("✅ Video is valid and playable!")
                
                # Get video info
                import json
                info = json.loads(probe_result.stdout)
                if 'streams' in info and len(info['streams']) > 0:
                    stream = info['streams'][0]
                    print(f"   - Codec: {stream.get('codec_name', 'unknown')}")
                    print(f"   - Resolution: {stream.get('width', '?')}x{stream.get('height', '?')}")
                    print(f"   - Duration: {info.get('format', {}).get('duration', 'unknown')} seconds")
            else:
                print("❌ Video validation failed")
                print(f"Error: {probe_result.stderr}")
        else:
            print("❌ FFmpeg conversion failed")
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error in video assembly: {e}")

async def test_individual_chunks():
    """Test individual H.264 chunks"""
    print("\n🔍 Testing Individual H.264 Chunks")
    print("=" * 40)
    
    video_dir = Path("recordings/original_video")
    h264_files = sorted(list(video_dir.glob("video_chunk_*.h264")))
    
    for i, h264_file in enumerate(h264_files[:3]):  # Test first 3 chunks
        print(f"\nTesting chunk {i+1}: {h264_file.name}")
        
        # Try to get info about the chunk
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(h264_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Chunk is valid H.264")
        else:
            print("❌ Chunk validation failed")
            print(f"Error: {result.stderr}")

if __name__ == "__main__":
    asyncio.run(test_video_assembly())
    asyncio.run(test_individual_chunks())
    
    print("\n" + "=" * 50)
    print("Video assembly test completed!")
